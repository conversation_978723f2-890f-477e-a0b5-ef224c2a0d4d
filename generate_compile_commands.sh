#!/bin/bash

# Script to generate compile_commands.json for clangd using bear
# This script wraps the normal build process to capture compilation commands

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Generating compile_commands.json for clangd ===${NC}"

# Check if bear is installed
if ! command -v bear &> /dev/null; then
    echo -e "${RED}Error: bear is not installed. Please install it first:${NC}"
    echo "  sudo apt install bear"
    exit 1
fi

# Get the project root directory
PROJECT_ROOT=$(pwd)
echo -e "${YELLOW}Project root: ${PROJECT_ROOT}${NC}"

# Check if we're in the right directory
if [ ! -f "build/envsetup.sh" ] || [ ! -f "build.sh" ]; then
    echo -e "${RED}Error: This script must be run from the project root directory${NC}"
    echo "Expected files: build/envsetup.sh, build.sh"
    exit 1
fi

# Source the environment setup
echo -e "${YELLOW}Sourcing environment setup...${NC}"
source build/envsetup.sh

# Clean previous build to ensure all files are compiled
echo -e "${YELLOW}Cleaning previous build...${NC}"
./build.sh clean

# Remove any existing compile_commands.json
if [ -f "compile_commands.json" ]; then
    echo -e "${YELLOW}Removing existing compile_commands.json${NC}"
    rm -f compile_commands.json
fi

# Ask user what to build
echo -e "${YELLOW}What would you like to build?${NC}"
echo "1) Full build (kernel + buildroot + platform) - takes longer but captures everything"
echo "2) Kernel only - faster, captures kernel and driver compilation"
echo "3) Platform only - captures userspace application compilation"
read -p "Enter your choice (1-3) [default: 2]: " BUILD_CHOICE

case "${BUILD_CHOICE:-2}" in
    1)
        echo -e "${YELLOW}Running full build with bear...${NC}"
        bear --output "${PROJECT_ROOT}/compile_commands.json" --append -- ./build.sh
        ;;
    2)
        echo -e "${YELLOW}Running kernel build with bear...${NC}"
        bear --output "${PROJECT_ROOT}/compile_commands.json" --append -- ./build.sh kernel
        ;;
    3)
        echo -e "${YELLOW}Running platform build with bear...${NC}"
        # First build kernel dependencies
        ./build.sh kernel
        # Then capture platform compilation
        bear --output "${PROJECT_ROOT}/compile_commands.json" --append -- make -C platform
        ;;
    *)
        echo -e "${RED}Invalid choice. Defaulting to kernel build.${NC}"
        bear --output "${PROJECT_ROOT}/compile_commands.json" --append -- ./build.sh kernel
        ;;
esac

# Check if compile_commands.json was generated
if [ ! -f "compile_commands.json" ]; then
    echo -e "${RED}Error: compile_commands.json was not generated${NC}"
    exit 1
fi

# Get statistics about the generated file
COMPILE_COMMANDS_COUNT=$(jq length compile_commands.json 2>/dev/null || echo "unknown")
FILE_SIZE=$(du -h compile_commands.json | cut -f1)

echo -e "${GREEN}=== Success! ===${NC}"
echo -e "${GREEN}compile_commands.json generated successfully${NC}"
echo -e "Location: ${PROJECT_ROOT}/compile_commands.json"
echo -e "File size: ${FILE_SIZE}"
echo -e "Number of compilation units: ${COMPILE_COMMANDS_COUNT}"

# Verify the content briefly
echo -e "\n${YELLOW}Sample entries from compile_commands.json:${NC}"
if command -v jq &> /dev/null; then
    echo "First few entries:"
    jq '.[0:3] | .[] | {file: .file, directory: .directory}' compile_commands.json 2>/dev/null || echo "Could not parse JSON with jq"
else
    echo "First few lines:"
    head -20 compile_commands.json
fi

echo -e "\n${GREEN}You can now use this compile_commands.json with clangd for better code completion and analysis.${NC}"
echo -e "${YELLOW}Note: Place this file in your project root or configure your editor to find it.${NC}"
